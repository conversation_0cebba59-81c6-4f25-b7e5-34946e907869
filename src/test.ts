import { GoogleSearchService } from "./services/google-search";
import { ContentExtractor } from "./services/content-extractor";

const googleSearchService = new GoogleSearchService();
const contentExtractor = new ContentExtractor();

process.env.http_proxy = "http://localhost:1086";
process.env.https_proxy = "http://localhost:1086";

async function main() {
  // const results = await googleSearchService.search(
  //   "武汉今天的天气如何？",
  //   5,
  //   {
  //     language: "zh-CN",
  //     dateRestrict: "m1",
  //     resultType: "webpages",
  //     page: 1,
  //     resultsPerPage: 5,
  //     sort: "date:r",
  //   }
  // );

  // console.log(results);

  const content = await contentExtractor.extractContent("https://news.ycombinator.com", "markdown");

  console.log(content);
}

main();
