{"compilerOptions": {"target": "es2021", "lib": ["es2021"], "jsx": "react-jsx", "module": "es2022", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "allowJs": true, "checkJs": false, "noEmit": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "types": ["./worker-configuration.d.ts", "node"]}, "include": ["worker-configuration.d.ts", "src/**/*.ts"]}