{"name": "google-search-engine-mcp-server", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "format": "biome format --write", "lint:fix": "biome lint --fix", "start": "wrangler dev", "cf-typegen": "wrangler types", "type-check": "tsc --noEmit", "inspector": "npx @modelcontextprotocol/inspector"}, "dependencies": {"@cloudflare/workers-oauth-provider": "^0.0.5", "@modelcontextprotocol/sdk": "^1.12.1", "@mozilla/readability": "^0.6.0", "agents": "^0.0.95", "biome": "^0.3.3", "cheerio": "^1.1.0", "dompurify": "^3.2.6", "hono": "^4.7.11", "jsdom": "^26.1.0", "markdown-it": "^14.1.0", "readability": "^0.1.0", "turndown": "^7.2.0", "zod": "^3.25.61"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.28.0", "@types/cheerio": "^1.0.0", "@types/dompurify": "^3.2.0", "@types/jsdom": "^21.1.7", "@types/markdown-it": "^14.1.2", "@types/node": "^24.0.1", "@types/turndown": "^5.0.5", "globals": "^16.2.0", "marked": "^15.0.12", "typescript": "^5.8.3", "workers-mcp": "^0.0.13", "wrangler": "^4.19.2"}}